{"name": "meeting", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite", "prod": "vite -m production", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@vueuse/core": "^10.11.0", "axios": "^1.7.2", "dayjs": "^1.11.12", "pinia": "^2.1.7", "vue": "^3.4.29", "vue-i18n": "^11.1.11", "vue-router": "^4.3.3", "webrtc-adapter": "^9.0.1"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@rushstack/eslint-patch": "^1.8.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tsconfig/node20": "^20.1.4", "@types/jsdom": "^21.1.7", "@types/node": "^20.14.5", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.19", "eslint-plugin-vue": "^9.23.0", "jsdom": "^24.1.0", "less": "^4.2.0", "npm-run-all2": "^6.2.0", "postcss": "^8.4.40", "prettier": "^3.2.5", "tailwindcss": "^3.4.7", "typescript": "~5.4.0", "vite": "^5.3.1", "vite-plugin-vue-devtools": "^7.3.1", "vitest": "^1.6.0", "vue-tsc": "^2.0.21"}}