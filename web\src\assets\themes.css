/* 全局主题样式 */
:root {
  /* 亮色主题变量 */
  --color-light-primary: #3b82f6;
  --color-light-secondary: #64748b;
  --color-light-background: #f8fafc;
  --color-light-surface: #ffffff;
  --color-light-text: #1e293b;
  --color-light-border: #e2e8f0;
  
  /* 暗色主题变量 */
  --color-dark-primary: #3b82f6;
  --color-dark-secondary: #94a3b8;
  --color-dark-background: #0f172a;
  --color-dark-surface: #1e293b;
  --color-dark-text: #f1f5f9;
  --color-dark-border: #334155;
}

/* 亮色主题 */
.light {
  --color-primary: var(--color-light-primary);
  --color-secondary: var(--color-light-secondary);
  --color-background: var(--color-light-background);
  --color-surface: var(--color-light-surface);
  --color-text: var(--color-light-text);
  --color-border: var(--color-light-border);
}

/* 暗色主题 */
.dark {
  --color-primary: var(--color-dark-primary);
  --color-secondary: var(--color-dark-secondary);
  --color-background: var(--color-dark-background);
  --color-surface: var(--color-dark-surface);
  --color-text: var(--color-dark-text);
  --color-border: var(--color-dark-border);
}

/* 全局样式 */
body {
  background-color: var(--color-background);
  color: var(--color-text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 玻璃态卡片样式 */
.glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 暗色主题下的玻璃态卡片 */
.dark .glass-card {
  background: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(51, 65, 85, 0.2);
}

/* 主要按钮样式 - 参照ui.html设计 */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  transition: all 0.3s ease;
  color: white;
  font-weight: 500;
  border-radius: 0.5rem; /* 8px */
  padding: 0.75rem 1rem; /* 12px 16px */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 44px; /* 参照ui.html中的按钮高度 */
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(37, 99, 235, 0.2);
}

/* 工具栏按钮样式 - 参照ui.html设计 */
.toolbar-btn {
  transition: all 0.2s ease;
  border-radius: 0.5rem; /* 8px */
  padding: 0.5rem; /* 8px */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px; /* 参照ui.html中的按钮宽度 */
  height: 40px; /* 参照ui.html中的按钮高度 */
}

.light .toolbar-btn:hover {
  background-color: #f1f5f9;
  transform: scale(1.05);
}

.dark .toolbar-btn:hover {
  background-color: #334155;
  transform: scale(1.05);
}

/* 输入框样式 - 参照ui.html设计 */
.input-field {
  transition: all 0.3s ease;
  border: 1px solid var(--color-border);
  background-color: var(--color-surface);
  color: var(--color-text);
  border-radius: 0.5rem; /* 8px */
  padding: 0.75rem 1rem; /* 12px 16px */
  width: 100%;
  height: 44px; /* 参照ui.html中的输入框高度 */
  font-size: 1rem; /* 16px */
}

.input-field:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  outline: none;
}

/* 视频容器样式 */
.video-container {
  aspect-ratio: 16/9;
  border-radius: 0.75rem;
  overflow: hidden;
}

.light .video-container {
  background-color: #e2e8f0;
}

.dark .video-container {
  background-color: #334155;
}

/* 用户列表项样式 */
.user-list-item {
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
}

.light .user-list-item:hover {
  background-color: #f8fafc;
}

.dark .user-list-item:hover {
  background-color: #334155;
}

/* 聊天消息样式 */
.chat-message {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 激活标签样式 */
.tab-active {
  border-bottom: 2px solid var(--color-primary);
  color: var(--color-primary);
}